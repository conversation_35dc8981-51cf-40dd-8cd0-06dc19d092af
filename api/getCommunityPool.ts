export interface CommunityPool {
  denom: string;
  amount: string;
}

export interface CommunityPoolResponse {
  pool: Array<CommunityPool>;
}

export const handleGetCommunityPool =
  async (): Promise<CommunityPoolResponse> => {
    const response = await fetch(
      "https://lcd-testnet.empe.io/cosmos/distribution/v1beta1/community_pool",
      {
        method: "GET",
        headers: {
          accept: "application/json",
        },
      }
    );

    return response.json();
  };
