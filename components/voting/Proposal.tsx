import { useState, useEffect } from "react";
import { ProposalStatus } from "interchain-query/cosmos/gov/v1/gov";
import { Status } from "./Status/Status";
import { Proposal as IProposal } from "interchain-query/cosmos/gov/v1/gov";
import { useVoting, Votes } from "@/hooks";
import { GovernanceVoteType } from "@interchain-ui/react";
import { ButtonGradient } from "@empe/front-kit-next-ui";
import { percent } from "@/utils";
import { useProposalMetadata } from "@/hooks/voting/useProposalMetadata";
import { decodeProposal } from "@/utils/decodeProposal";
import ReactMarkdown from "react-markdown";

export type ProposalProps = {
  proposal: IProposal;
  votes?: Votes;
  quorum?: number;
  bondedTokens?: string;
  chainName: string;
  onVoteSuccess?: () => void;
};

const VoteTypes = ["", "yes", "abstain", "no", "noWithVeto"];

export function Proposal({
  votes,
  quorum,
  proposal,
  chainName,
  bondedTokens,
  onVoteSuccess = () => {},
}: ProposalProps) {
  const vote = votes?.[proposal.id.toString()];

  console.log("propo:", proposal);

  const [voteType, setVoteType] = useState<GovernanceVoteType>();

  const { proposalDescription, proposalTitle } = useProposalMetadata({
    proposal,
  });

  const { isVoting, onVote } = useVoting({ chainName, proposal });

  useEffect(() => {
    if (typeof vote === "number") {
      setVoteType(VoteTypes[vote] as GovernanceVoteType);
    }
  }, [vote]);

  const isChanged =
    (vote === undefined && voteType) ||
    (typeof vote === "number" && voteType && voteType !== VoteTypes[vote]);

  const isVotingPeriod =
    proposal.status === ProposalStatus.PROPOSAL_STATUS_VOTING_PERIOD;

  const total = proposal.finalTallyResult
    ? Object.values(proposal.finalTallyResult).reduce(
        (sum, val) => sum + Number(val),
        0
      )
    : 0;

  const normalizedTotal = total / 1_000_000;
  const turnoutPercentage =
    bondedTokens && Number(bondedTokens) > 0
      ? Math.min((normalizedTotal / Number(bondedTokens)) * 100, 100).toFixed(2)
      : "0.00";

  // @ts-ignore
  const description = proposal.summary || "";

  // const renderedDescription =
  //   description.length > 200
  //     ? showMore
  //       ? description
  //       : `${description.slice(0, 200)}...`
  //     : description || "";

  console.log("proposal:", proposal);
  console.dir(proposal, { depth: null });
  const voteResults = [
    {
      name: "Yes",
      value: percent(proposal.finalTallyResult?.yesCount, total),
      color: "bg-green-500",
      empe: proposal.finalTallyResult?.yesCount || "0",
    },
    {
      name: "Abstain",
      value: percent(proposal.finalTallyResult?.abstainCount, total),
      color: "bg-main-100",
      empe: proposal.finalTallyResult?.abstainCount || "0",
    },
    {
      name: "No",
      value: percent(proposal.finalTallyResult?.noCount, total),
      color: "bg-red-500",
      empe: proposal.finalTallyResult?.noCount || "0",
    },
    {
      name: "No with Veto",
      value: percent(proposal.finalTallyResult?.noWithVetoCount, total),
      color: "bg-amber-500",
      empe: proposal.finalTallyResult?.noWithVetoCount || "0",
    },
  ];

  function onVoteTypeChange(selected: string) {
    setVoteType(selected as GovernanceVoteType);
  }

  function onVoteButtonClick() {
    if (!voteType) return;

    onVote({
      option: VoteTypes.indexOf(voteType),
      success: onVoteSuccess,
    });
  }

  const renderProposalSchedule = () => {
    const message = proposal.messages[0];
    const decoded = decodeProposal(message);

    if (!decoded) return <p className="text-sm">Cannot decode message</p>;

    return (
      <div className="flex flex-col gap-1">
        <p className="text-sm text-main-800">Description:</p>
        <p className="text-sm font-semibold">{decoded.title}</p>
        {/* <p className="text-sm whitespace-pre-wrap">{decoded.description}</p> */}
        <ReactMarkdown>{decoded.description}</ReactMarkdown>
      </div>
    );
  };

  const renderProposalVoting = () => {
    if (!isVotingPeriod) return null;
    return (
      <>
        <div className="flex flex-col gap-4">
          <div className="text-gray-400 flex flex-col gap-2">
            <p className="text-gray-500">Voting:</p>
            {VoteTypes.slice(1).map((voteOption) => (
              <label
                key={voteOption}
                className="flex items-center space-x-2 cursor-pointer"
              >
                <input
                  type="radio"
                  name="vote"
                  value={voteOption}
                  checked={voteType === voteOption}
                  onChange={(e) => onVoteTypeChange(e.target.value)}
                  className="appearance-none w-4 h-4 border border-gray-500 rounded-full checked:bg-white checked:ring-2 checked:ring-white"
                />
                <span className="text-white">
                  {voteOption.charAt(0).toUpperCase() + voteOption.slice(1)}
                </span>
              </label>
            ))}
          </div>
          <ButtonGradient
            onClick={onVoteButtonClick}
            isLoading={isVoting}
            disabled={
              !isVotingPeriod || isVoting || (isVotingPeriod && !isChanged)
            }
          >
            {vote ? "Update Vote" : "Vote"}
          </ButtonGradient>
        </div>
      </>
    );
  };

  const renderProposalStatus = () => {
    return (
      <>
        <div className="flex flex-col md:flex-row w-full gap-10 h-full">
          <div className="flex flex-col gap-10 flex-1">
            {voteResults.map((result) => (
              <div key={result.name} className="flex flex-col gap-1">
                <div className="flex justify-between w-full px-2">
                  <span>{result.name}</span>
                  <span>{result.value.toFixed(2)}%</span>
                </div>
                <div className="w-full bg-gray-700 h-1 rounded-lg overflow-hidden">
                  <div
                    className={`${result.color} h-full`}
                    style={{ width: `${result.value}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-400 px-2">
                  {(Number(result.empe) / 1_000_000).toFixed(2)} EMPE
                </div>
              </div>
            ))}
          </div>

          {/* Status */}
          <Status
            total={total}
            proposal={proposal}
            turnoutPercentage={turnoutPercentage}
          />
        </div>

        <div
          className="border border-b border-main-1400"
          style={{ borderWidth: "0.5px" }}
        />
      </>
    );
  };

  return (
    <div className="text-white flex flex-col gap-8">
      <div className="flex flex-col gap-2">
        <div className="border-t pt-8 border-main-1400">
          <p className="text-main-800">Subject of voting:</p>
          <p className="text-sm">{proposalTitle}</p>
        </div>
      </div>
      <div
        className="border border-b border-main-1400"
        style={{ borderWidth: "0.5px" }}
      />
      {renderProposalSchedule()}

      {/* Wykres głosowania */}
      {renderProposalStatus()}

      {/* Głosowanie */}
      {renderProposalVoting()}
    </div>
  );
}
