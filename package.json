{"name": "@cosmology/chain-template", "version": "1.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "locks:remove": "rm -f yarn.lock", "locks:create": "generate-lockfile --lockfile ../../yarn.lock --package package.json --write yarn.lock --force", "locks": "npm run locks:remove && npm run locks:create", "starship": "starship --config starship/configs/config.yaml"}, "dependencies": {"@chain-registry/assets": "1.70.130", "@cosmjs/amino": "0.33.1", "@cosmjs/cosmwasm-stargate": "0.33.1", "@cosmjs/proto-signing": "^0.34.0", "@cosmjs/stargate": "^0.34.0", "@cosmos-kit/cosmos-extension-metamask": "^0.12.7", "@cosmos-kit/okxwallet": "^2.11.6", "@cosmos-kit/react": "2.21.2", "@empe/empejs": "0.2.0", "@empe/front-kit-next-ui": "0.4.0", "@empe/front-kit-tailwind-config": "0.4.0", "@empe/identity": "2.2.0", "@interchain-ui/react": "1.23.31", "@interchain-ui/react-no-ssr": "0.1.2", "@metamask/sdk-react": "^0.31.5", "@next/third-parties": "^15.3.2", "@okxconnect/universal-provider": "^1.7.8", "@tanstack/react-query": "4.32.0", "@types/qrcode": "^1.5.5", "ace-builds": "1.35.0", "bignumber.js": "9.1.2", "chain-registry": "^1.69.130", "chart.js": "^4.4.7", "clsx": "^2.1.1", "cosmos-kit": "2.23.4", "dayjs": "1.11.11", "ethers": "^6.13.5", "framer-motion": "^11.18.0", "graphql": "^16.10.0", "graphql-request": "^7.1.2", "graphql-ws": "^6.0.3", "interchain-query": "1.10.1", "next": "^13", "node-gzip": "^1.1.2", "osmo-query": "16.5.1", "protobufjs": "^7.5.3", "qrcode": "^1.5.4", "react": "18.2.0", "react-ace": "11.0.1", "react-chartjs-2": "^5.3.0", "react-dom": "18.2.0", "react-dropzone": "^14.2.3", "react-icons": "5.2.1", "react-markdown": "^10.1.0", "sharp": "^0.33.5", "zustand": "4.5.2"}, "devDependencies": {"@chain-registry/types": "^0.50.78", "@keplr-wallet/types": "^0.12.111", "@starship-ci/cli": "^2.9.0", "@tanstack/react-query-devtools": "4.32.0", "@types/node": "18.11.9", "@types/node-gzip": "^1", "@types/react": "18.0.25", "@types/react-dom": "18.0.9", "autoprefixer": "^10.4.20", "eslint": "8.28.0", "eslint-config-next": "13.0.5", "generate-lockfile": "0.0.12", "postcss": "^8.5.2", "starshipjs": "^2.4.0", "tailwindcss": "3", "typescript": "4.9.3", "yaml-loader": "^0.8.1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}