syntax = "proto3";
package cosmos.upgrade.v1beta1;

import "gogoproto/gogo.proto";
import "cosmos_proto/cosmos.proto";
import "cosmos/upgrade/v1beta1/upgrade.proto";
import "cosmos/msg/v1/msg.proto";
import "amino/amino.proto";

option go_package = "github.com/cosmos/cosmos-sdk/x/upgrade/types";

// Msg defines the upgrade Msg service.
service Msg {
  option (cosmos.msg.v1.service) = true;

  // SoftwareUpgrade is a governance operation for initiating a software upgrade.
  rpc SoftwareUpgrade(MsgSoftwareUpgrade) returns (MsgSoftwareUpgradeResponse) {
    option (cosmos_proto.method_added_in) = "cosmos-sdk 0.46";
  };

  // CancelUpgrade is a governance operation for cancelling a previously
  // approved software upgrade.
  rpc CancelUpgrade(MsgCancelUpgrade) returns (MsgCancelUpgradeResponse) {
    option (cosmos_proto.method_added_in) = "cosmos-sdk 0.46";
  };
}

// MsgSoftwareUpgrade is the Msg/SoftwareUpgrade request type.
message MsgSoftwareUpgrade {
  option (cosmos_proto.message_added_in) = "cosmos-sdk 0.46";
  option (cosmos.msg.v1.signer)          = "authority";
  option (amino.name)                    = "cosmos-sdk/MsgSoftwareUpgrade";

  // authority is the address that controls the module (defaults to x/gov unless overwritten).
  string authority = 1 [(cosmos_proto.scalar) = "cosmos.AddressString"];

  // plan is the upgrade plan.
  Plan plan = 2 [(gogoproto.nullable) = false, (amino.dont_omitempty) = true];
}

// MsgSoftwareUpgradeResponse is the Msg/SoftwareUpgrade response type.
message MsgSoftwareUpgradeResponse {
  option (cosmos_proto.message_added_in) = "cosmos-sdk 0.46";
}

// MsgCancelUpgrade is the Msg/CancelUpgrade request type.
message MsgCancelUpgrade {
  option (cosmos_proto.message_added_in) = "cosmos-sdk 0.46";
  option (cosmos.msg.v1.signer)          = "authority";
  option (amino.name)                    = "cosmos-sdk/MsgCancelUpgrade";

  // authority is the address that controls the module (defaults to x/gov unless overwritten).
  string authority = 1 [(cosmos_proto.scalar) = "cosmos.AddressString"];
}

// MsgCancelUpgradeResponse is the Msg/CancelUpgrade response type.
message MsgCancelUpgradeResponse {
  option (cosmos_proto.message_added_in) = "cosmos-sdk 0.46";
}
