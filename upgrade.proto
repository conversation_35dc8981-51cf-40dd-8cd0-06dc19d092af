syntax = "proto3";
package cosmos.upgrade.v1beta1;

import "google/protobuf/any.proto";
import "gogoproto/gogo.proto";
import "google/protobuf/timestamp.proto";
import "cosmos_proto/cosmos.proto";
import "amino/amino.proto";

option go_package                      = "github.com/cosmos/cosmos-sdk/x/upgrade/types";
option (gogoproto.goproto_getters_all) = false;

// Plan specifies information about a planned upgrade and when it should occur.
message Plan {
  option (amino.name)      = "cosmos-sdk/Plan";
  option (gogoproto.equal) = true;

  // Sets the name for the upgrade. This name will be used by the upgraded
  // version of the software to apply any special "on-upgrade" commands during
  // the first BeginBlock method after the upgrade is applied. It is also used
  // to detect whether a software version can handle a given upgrade. If no
  // upgrade handler with this name has been set in the software, it will be
  // assumed that the software is out-of-date when the upgrade Time or Height is
  // reached and the software will exit.
  string name = 1;

  // Deprecated: Time based upgrades have been deprecated. Time based upgrade logic
  // has been removed from the SDK.
  // If this field is not empty, an error will be thrown.
  google.protobuf.Timestamp time = 2
      [deprecated = true, (gogoproto.stdtime) = true, (gogoproto.nullable) = false, (amino.dont_omitempty) = true];

  // The height at which the upgrade must be performed.
  int64 height = 3;

  // Any application specific upgrade info to be included on-chain
  // such as a git commit that validators could automatically upgrade to
  string info = 4;

  // Deprecated: UpgradedClientState field has been deprecated. IBC upgrade logic has been
  // moved to the IBC module in the sub module 02-client.
  // If this field is not empty, an error will be thrown.
  google.protobuf.Any upgraded_client_state = 5 [deprecated = true];
}

// SoftwareUpgradeProposal is a gov Content type for initiating a software
// upgrade.
// Deprecated: This legacy proposal is deprecated in favor of Msg-based gov
// proposals, see MsgSoftwareUpgrade.
message SoftwareUpgradeProposal {
  option deprecated                          = true;
  option (cosmos_proto.implements_interface) = "cosmos.gov.v1beta1.Content";
  option (amino.name)                        = "cosmos-sdk/SoftwareUpgradeProposal";
  option (gogoproto.equal)                   = true;

  // title of the proposal
  string title = 1;

  // description of the proposal
  string description = 2;

  // plan of the proposal
  Plan plan = 3 [(gogoproto.nullable) = false, (amino.dont_omitempty) = true];
}

// CancelSoftwareUpgradeProposal is a gov Content type for cancelling a software
// upgrade.
// Deprecated: This legacy proposal is deprecated in favor of Msg-based gov
// proposals, see MsgCancelUpgrade.
message CancelSoftwareUpgradeProposal {
  option deprecated                          = true;
  option (cosmos_proto.implements_interface) = "cosmos.gov.v1beta1.Content";
  option (amino.name)                        = "cosmos-sdk/CancelSoftwareUpgradeProposal";
  option (gogoproto.equal)                   = true;

  // title of the proposal
  string title = 1;

  // description of the proposal
  string description = 2;
}

// ModuleVersion specifies a module and its consensus version.
message ModuleVersion {
  option (cosmos_proto.message_added_in) = "cosmos-sdk 0.43";
  option (gogoproto.equal)               = true;

  // name of the app module
  string name = 1;

  // consensus version of the app module
  uint64 version = 2;
}
