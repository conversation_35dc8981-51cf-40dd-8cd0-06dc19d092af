export interface BondedTokensResponse {
  pool: {
    not_bonded_tokens: string;
    bonded_tokens: string;
  };
}

export const handleGetBonded = async (): Promise<BondedTokensResponse> => {
  const response = await fetch(
    "https://lcd-testnet.empe.io/cosmos/staking/v1beta1/pool",
    {
      method: "GET",
      headers: {
        accept: "application/json",
      },
    }
  );

  return response.json();
};
