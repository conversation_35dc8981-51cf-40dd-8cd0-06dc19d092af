import { useEffect, useState } from "react";
import protobuf from "protobufjs";

export default function ProposalMessageDecoder({
  rawValue,
}: {
  rawValue: Uint8Array;
}) {
  const [decoded, setDecoded] = useState<any>(null);

  useEffect(() => {
    const run = async () => {
      const root = protobuf.Root.fromJSON({});

      // <PERSON><PERSON><PERSON> to proposal wiadomo<PERSON>ć, np. SoftwareUpgradeProposal:
      const MessageType = root.lookupType(
        "cosmos.upgrade.v1beta1.SoftwareUpgradeProposal"
      );

      try {
        const decodedMessage = MessageType.decode(rawValue);

        setDecoded(
          MessageType.toObject(decodedMessage, {
            longs: String,
            enums: String,
            bytes: String,
          })
        );
      } catch (err) {
        console.error("Błąd dekodowania:", err);
      }
    };

    run();
  }, [rawValue]);

  if (!decoded) return <p>Decoding...</p>;

  return <pre>{JSON.stringify(decoded, null, 2)}</pre>;
}
