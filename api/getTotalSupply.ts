export interface TotalSupplyResponse {
  amount: {
    denom: string;
    amount: string;
  };
}

export const handleGetTotalSupply = async (): Promise<TotalSupplyResponse> => {
  const response = await fetch(
    "https://lcd-testnet.empe.io/cosmos/bank/v1beta1/supply/by_denom?denom=uempe",
    {
      method: "GET",
      headers: {
        accept: "application/json",
      },
    }
  );

  return response.json();
};
