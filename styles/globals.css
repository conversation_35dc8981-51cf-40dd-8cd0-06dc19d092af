@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

.without-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.validator-list tbody tr:not(:last-child) {
  border-bottom: 1px solid #37383e;
}

.validator-list td:not(:last-child),
.validator-list th:not(:last-child) {
  border-right: 1px solid #37383e;
}

.validator-list td {
  padding: 24px;
}

@media (max-width: 768px) {
  .validator-list td {
    padding: 20px;
  }
}

.mylist-bg {
  background-color: #37383e;
}

.sort-icon {
  transform: scale(0.75);
}
